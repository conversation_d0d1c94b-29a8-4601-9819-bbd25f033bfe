require('dotenv').config();
require('newrelic');
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const morgan = require('morgan');
const winstonLogger = require('./Logger/logger');

require('./Configs/globals');
// require('./Configs/cron-job');

const SHOULD_RUN_ON_HTTP = process.env.SHOULD_RUN_ON_HTTP;
const http = SHOULD_RUN_ON_HTTP == 'true' ? require('http') : require('https');

const app = express();

// Replace morgan's default logger with winston
const morganStream = {
  write: (message) => winstonLogger.info(message.trim()),
};
app.use(morgan('combined', { stream: morganStream }));
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
app.use(express.json());

const server = http.createServer(app);

// ------* Language *------- //
const language = require('i18n');
language.configure({
  locales: ['en'],
  defaultLocale: 'en',
  autoReload: true,
  directory: __dirname + '/Locales',
  queryParameter: 'lang',
  objectNotation: true,
  syncFiles: true,
});
app.use(language.init); // MULTILINGUAL SETUP

// ------- Response Handler --------- //
app.use((req, res, next) => {
  const ResponseHandler = require('./Configs/responseHandler');
  res.handler = new ResponseHandler(req, res);
  next();
});

// ------- Routes --------- //
const appRoutes = require('./Routes');

// -------   GLOBAL ERROR HANDLER --------- //
app.use((err, req, res, next) => {
  if (res.headersSent) {
    return next(err);
  }
  if (!res.handler) {
    const ResponseHandler = require('./Configs/responseHandler');
    res.handler = new ResponseHandler(req, res);
    return res.handler.serverError(undefined, undefined, undefined, err);
  }
  return res.handler.serverError(undefined, undefined, undefined, err);
});

appRoutes(app);

require('./Crons');

server.listen(process.env.PORT, async () => {
  console.log(`Server running at ${process.env.PORT}`);
});
