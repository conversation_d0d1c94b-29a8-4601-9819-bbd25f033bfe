const UserSubscriptionModel =
  new (require('../../Models/App/UserSubscriptionModel'))();
const { Op } = require('sequelize');
const { STATUS_CODES } = require('../../Configs/constants');
const { API } = require('../../Configs/message');
const { getSubscriptionDetails } = require('../../Helpers/gcp.helper');

class WebHookController {
  async webhook(req, res) {
    const { message } = req.body;

    if (!message) {
      return res.handler.custom(
        STATUS_CODES.BAD_REQUEST,
        API.INVALID_MESSAGE_FORMAT
      );
    }

    const jsonData = Buffer.from(message.data, 'base64').toString('utf8');
    const notification = JSON.parse(jsonData);

    console.log('notification: ');
    console.dir(notification, { depth: 100 });

    const { subscriptionNotification, testNotification, voidedPurchaseNotification } = notification;

    if (testNotification) {
      console.log('testNotification: ');
      console.dir(testNotification, { depth: 100 });
      return res.handler.success(API.WEBHOOK_PROCESSED_SUCCESSFULLY);
    }

    if (voidedPurchaseNotification) {
      console.log('voidedPurchaseNotification: ');
      console.dir(voidedPurchaseNotification, { depth: 100 });
      return res.handler.success(API.WEBHOOK_PROCESSED_SUCCESSFULLY);
    }

    if (!subscriptionNotification) {
      return res.handler.custom(
        STATUS_CODES.BAD_REQUEST,
        API.INVALID_MESSAGE_FORMAT
      );
    }

    console.log('subscriptionNotification: ');
    console.dir(subscriptionNotification, { depth: 100 });

    const subscriptionDetails = await getSubscriptionDetails(
      // subscriptionNotification.subscriptionId,
      subscriptionNotification.purchaseToken
    );

    console.log('subscriptionDetails: ');
    console.dir(subscriptionDetails, { depth: 100 });

    // return res.handler.custom(
    //   STATUS_CODES.BAD_REQUEST,
    //   API.INVALID_MESSAGE_FORMAT
    // );

    const subscriptionId = subscriptionNotification.subscriptionId;
    const purchaseToken = subscriptionNotification.purchaseToken;

    const purchaseDetails = subscriptionDetails.lineItems[0];

    const startDate = new Date(subscriptionDetails.startTime);
    const expirationDate = new Date(purchaseDetails.expiryTime);

    // const sixtyDaysLater = new Date(
    //   expirationDate.getTime() + 60 * 24 * 60 * 60 * 1000
    // );

    const sixtyDaysLater = new Date(expirationDate.getTime() + 30 * 60 * 1000);

    switch (subscriptionNotification.notificationType) {
      case 1:
      case 2:
      case 4:
      case 7:
        // break;
        const userId =
          subscriptionDetails.externalAccountIdentifiers[
            'obfuscatedExternalAccountId'
          ];

        const existingSub = await UserSubscriptionModel.findSubscriptionByField(
          {
            // user_id: userId,
            subscription_id: subscriptionId,
            purchase_token: purchaseToken,
          }
        );

        if (existingSub) {
          // Update existing record
          await UserSubscriptionModel.updateSubscription(
            {
              transaction_id:
                subscriptionDetails?.latestOrderId ||
                purchaseDetails?.latestSuccessfulOrderId,
              // payment_amount: (
              //   subscriptionDetails.priceAmountMicros / 1_000_000
              // ).toFixed(2),
              start_date: startDate,
              expiration_date: expirationDate,
              deletion_date: sixtyDaysLater,
            },
            {
              id: existingSub.id,
            }
          );
        } else {

          await UserSubscriptionModel.updateSubscription(
            {
              deletion_date: null,
            },
            {
              user_id: userId,
              [Op.not]: {
                deletion_date: null,
              },
            }
          );

          // Insert new record
          await UserSubscriptionModel.createSubscription({
            user_id: userId,
            subscription_id: subscriptionId,
            transaction_id:
              subscriptionDetails?.latestOrderId ||
              purchaseDetails?.latestSuccessfulOrderId,
            purchase_token: purchaseToken,
            // payment_amount: (
            //   subscriptionDetails.priceAmountMicros / 1_000_000
            // ).toFixed(2),
            start_date: startDate,
            expiration_date: expirationDate,
            deletion_date: sixtyDaysLater,
          });
        }

        break;
      case 3:
      case 13:
        // const userId =
        //   subscriptionDetails.externalAccountIdentifiers[
        //     'obfuscatedExternalAccountId'
        //   ];

        const sub = await UserSubscriptionModel.findSubscriptionByField({
          // user_id: userId,
          subscription_id: subscriptionId,
          purchase_token: purchaseToken,
        });

        if (sub) {
          // Update existing record
          await UserSubscriptionModel.updateSubscription(
            {
              deletion_date: sixtyDaysLater,
            },
            {
              id: sub.id,
            }
          );
        }

        break;

      default:
        console.log('Other notification type: ', notification);
        break;
    }

    return res.handler.success(API.WEBHOOK_PROCESSED_SUCCESSFULLY);
  }
}

module.exports = WebHookController;
