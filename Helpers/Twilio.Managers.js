const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const { combineCountryCodeWithPhoneNumber } =
  new (require('../Helpers/common'))();

module.exports = class {
  sendSms = (phone, message) => {
    try {
      const client = require('twilio')(accountSid, authToken);

      client.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phone,
      });
      console.log(`SMS sent successfully to ${phone}`);
    } catch (error) {
      console.error('Error sending SMS:', error);
      return;
    }
  };

  userVerificationOtp = (user = {}, otp = '') => {
    const phone = combineCountryCodeWithPhoneNumber(
      user?.country_code,
      user?.phone_number
    );
    const message = `Dear ${user?.name}, use this One Time Password ${otp} to verify your (Trackerbuttons) account. This OTP will be valid for the next 5 mins.”`;
    if (phone && otp) this?.sendSms(phone, message);
  };

  resetPasswordOtp = (user = {}, otp = '') => {
    const phone = combineCountryCodeWithPhoneNumber(
      user?.country_code,
      user?.phone_number
    );
    const message = `Dear ${user?.name}, use this One Time Password ${otp} to reset password verification for (Trackerbuttons) account. This OTP will be valid for the next 5 mins.”`;
    if (phone && otp) this?.sendSms(phone, message);
  };
};
